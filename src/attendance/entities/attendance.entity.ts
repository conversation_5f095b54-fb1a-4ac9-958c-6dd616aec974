import { ObjectType, Field, Int } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { Location } from 'src/location/entities/location.entity';
import { Shift } from 'src/shifts/entities/shift.entity';
import { User } from 'src/users/entities/user.entity';

@ObjectType({ description: 'User attendance' })
@Schema()
export class Attendance extends MongooseSchema {
  @Field(() => User)
  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId })
  user: string;

  @Field(() => Date)
  @Prop({ required: true })
  date: Date;

  @Field(() => Int)
  @Prop({ required: true })
  timeSpentInMinutes: number;

  @Field(() => Int)
  @Prop({ required: true })
  overTimeSpentInMinutes: number;

  @Field(() => Date)
  @Prop({ required: true })
  startTime: Date;

  @Field(() => Date, { nullable: true })
  @Prop()
  endTime?: Date;

  @Field(() => Date, { nullable: true })
  @Prop()
  overTime?: Date;

  @Field(() => Shift)
  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId })
  shift: string;

  @Field(() => Location)
  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId })
  location: string;
}

export const AttendanceSchema = SchemaFactory.createForClass(Attendance);

// Add compound index to prevent duplicate attendance records
// This ensures only one attendance record per user per shift (one clock-in policy)
AttendanceSchema.index(
  { user: 1, shift: 1 },
  {
    unique: true,
    name: 'unique_user_shift_attendance',
  },
);

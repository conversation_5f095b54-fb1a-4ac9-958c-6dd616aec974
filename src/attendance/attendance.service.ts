import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  differenceInMinutes,
  min,
  subMinutes,
  isBefore,
  isAfter,
} from 'date-fns';
import { FilterQuery, Model } from 'mongoose';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';
import { ShiftsService } from 'src/shifts/shifts.service';
import { ClockInInput, ClockOutInput } from './dto/clockin.input';
import { Attendance } from './entities/attendance.entity';
import { CreateAttendanceInput } from './dto/create-attendance.input';
import { Shift } from 'src/shifts/entities/shift.entity';
import { UpdateAttendanceInput } from './dto/update-attendance.input';
import { UsersService } from 'src/users/users.service';
import { AttendanceFilterInput } from './dto/attendance.input';
@Injectable()
export class AttendanceService {
  private readonly logger = new Logger(AttendanceService.name);

  constructor(
    @InjectModel(Attendance.name)
    private readonly attendance: Model<Attendance>,
    @InjectConnection()
    private readonly connection: Connection,
    private readonly shiftService: ShiftsService,
    private readonly usersService: UsersService,
  ) {}

  private timeAndOverTimeSpent({
    shift,
    actualClockOutTime,
  }: {
    shift: Shift;
    actualClockOutTime: Date;
  }) {
    // Calculate endTime: min(actualClockOutTime, shift.endDateTime)
    // If user clocks out before shift end → use actual time
    // If user clocks out after shift end → cap at shift end time
    const endTime = min([actualClockOutTime, shift.endDateTime]);

    // Calculate regular time spent from shift start to endTime
    const timeSpentInMinutes = differenceInMinutes(
      endTime,
      shift.startDateTime,
    );

    // Calculate overtime spent only if overtime exists and user worked past shift end
    let overTimeSpentInMinutes = 0;
    if (shift.overTime && isAfter(actualClockOutTime, shift.endDateTime)) {
      // Overtime is from shift end to min(actualClockOutTime, shift.overTime)
      overTimeSpentInMinutes = differenceInMinutes(
        min([actualClockOutTime, shift.overTime]),
        shift.endDateTime,
      );
    }

    return {
      timeSpentInMinutes,
      overTimeSpentInMinutes,
      endTime, // Return the calculated endTime for database storage
    };
  }

  private async verifyUserFace(userId: string, base64Img: string) {
    const user = await this.usersService.findOne({ _id: userId });
    if (!user?.faceInformation?.faceId) {
      throw new UnauthorizedException(
        'Face data not registered for attendance',
      );
    }

    const faceMatches = await this.usersService.searchFace(base64Img);
    const isValidFace = faceMatches?.FaceMatches?.some(
      (face) => face.Face?.ExternalImageId === userId,
    );

    if (!isValidFace) {
      throw new UnauthorizedException('Face verification failed');
    }
  }

  private async validateClockIn(
    userId: string,
    shiftId: string,
    date: Date,
    session?: any,
  ): Promise<void> {
    this.logger.debug(
      `Validating clock-in for user: ${userId}, shift: ${shiftId}, date: ${date.toISOString()}`,
    );

    // Get the shift details to check the time window
    const shift = await this.shiftService.findOne({ _id: shiftId });
    if (!shift) {
      throw new NotFoundException('Shift not found');
    }

    // Check if current time is within the allowed clock-in window
    const currentTime = new Date();
    const shiftStartWithBuffer = subMinutes(shift.startDateTime, 30); // 30 minutes buffer before shift start
    const shiftEnd = shift.endDateTime;

    // User can clock in from 30 minutes before shift start until shift end
    if (isBefore(currentTime, shiftStartWithBuffer)) {
      throw new ConflictException(
        'Cannot clock in more than 30 minutes before shift start time',
      );
    }

    if (isAfter(currentTime, shiftEnd)) {
      throw new ConflictException('Cannot clock in after shift end time');
    }

    // Check for ANY existing attendance record for this user and shift
    // This prevents multiple clock-ins per user per shift (one clock-in only policy)
    this.logger.debug(
      `Checking for any existing attendance for user: ${userId}, shift: ${shiftId}`,
    );
    const existingAttendance = await this.attendance
      .findOne({
        user: userId,
        shift: shiftId,
      })
      .session(session);

    if (existingAttendance) {
      this.logger.warn(
        `Found existing attendance record: ${existingAttendance._id} for user: ${userId}, shift: ${shiftId}`,
      );

      if (existingAttendance.endTime) {
        // User has already completed this shift
        throw new ConflictException(
          'User has already completed attendance for this shift. Only one clock-in per shift is allowed.',
        );
      } else {
        // User is currently clocked in
        throw new ConflictException(
          'User is already clocked in for this shift',
        );
      }
    }

    this.logger.debug(
      `Clock-in validation passed for user: ${userId}, shift: ${shiftId}`,
    );
  }

  private async validateClockOut(attendance: Attendance): Promise<void> {
    // Check if user has already clocked out
    if (attendance.endTime) {
      throw new ConflictException(
        'User has already clocked out for this shift',
      );
    }
  }

  async clockIn(
    userId: string,
    { date, locationId, shiftId, base64Img }: ClockInInput,
  ) {
    // Use a transaction to prevent race conditions
    const session = await this.connection.startSession();

    try {
      return await session.withTransaction(async () => {
        // Perform validation within the transaction
        await this.validateClockIn(userId, shiftId, date, session);
        await this.verifyUserFace(userId, base64Img);

        // Get shift details to set proper startTime
        const shift = await this.shiftService.findOne({ _id: shiftId });
        if (!shift) throw new NotFoundException('Shift not found');

        const actualClockInTime = new Date();

        // Create the attendance record within the transaction
        const attendanceRecord = await this.attendance.create(
          [
            {
              location: locationId,
              shift: shiftId,
              user: userId,
              date,
              timeSpentInMinutes: 0,
              overTimeSpentInMinutes: 0,
              startTime: shift.startDateTime, // Always use shift start time for calculations
              actualClockInTime, // Store actual clock-in time for auditing
              endTime: null,
              actualClockOutTime: null,
            },
          ],
          { session },
        );

        return attendanceRecord[0];
      });
    } finally {
      await session.endSession();
    }
  }

  async clockOut({ attendanceId }: ClockOutInput) {
    const attendance = await this.attendance.findById(attendanceId);
    if (!attendance) throw new NotFoundException('Attendance not found');

    await this.validateClockOut(attendance);

    const shift = await this.shiftService.findOne({ _id: attendance.shift });
    if (!shift) throw new NotFoundException('Shift not found');

    const actualClockOutTime = new Date();
    const { timeSpentInMinutes, overTimeSpentInMinutes, endTime } =
      this.timeAndOverTimeSpent({
        shift,
        actualClockOutTime,
      });

    return this.attendance.findByIdAndUpdate(attendanceId, {
      endTime,
      actualClockOutTime,
      timeSpentInMinutes,
      overTimeSpentInMinutes,
    });
  }

  async createAttendance(attendance: CreateAttendanceInput) {
    const { date, locationId, shiftId, userId, startTime, endTime, overTime } =
      attendance;

    const shift = await this.shiftService.findOne({ _id: shiftId });
    if (!shift) throw new NotFoundException('Shift not found');

    let timeSpentInMinutes = 0;
    let overTimeSpentInMinutes = 0;
    let calculatedEndTime = endTime;

    // Only calculate times if endTime is provided (completed attendance)
    if (endTime) {
      const result = this.timeAndOverTimeSpent({
        shift,
        actualClockOutTime: endTime,
      });
      timeSpentInMinutes = result.timeSpentInMinutes;
      overTimeSpentInMinutes = result.overTimeSpentInMinutes;
      calculatedEndTime = result.endTime;
    }

    return this.attendance.create({
      location: locationId,
      shift: shiftId,
      user: userId,
      date,
      startTime: shift.startDateTime, // Always use shift start time for calculations
      endTime: calculatedEndTime,
      overTime,
      actualClockInTime: startTime, // Store provided startTime as actual clock-in
      actualClockOutTime: endTime, // Store provided endTime as actual clock-out
      timeSpentInMinutes,
      overTimeSpentInMinutes,
    });
  }

  getAttendance(query: FilterQuery<Attendance> = {}) {
    return this.attendance.find(query);
  }

  findOne(filter: FilterQuery<Attendance>) {
    return this.attendance.findOne(filter);
  }

  async update(id: string, updateAttendanceInput: UpdateAttendanceInput) {
    const { id: _, ...updateData } = updateAttendanceInput;
    const { userId, date, startTime, endTime, overTime, locationId, shiftId } =
      updateData;

    const shift = await this.shiftService.findOne({ _id: shiftId });
    if (!shift) throw new NotFoundException('Shift not found');

    let timeSpentInMinutes = 0;
    let overTimeSpentInMinutes = 0;
    let calculatedEndTime = endTime;

    // Recalculate times if endTime is provided
    if (endTime) {
      const result = this.timeAndOverTimeSpent({
        shift,
        actualClockOutTime: endTime,
      });
      timeSpentInMinutes = result.timeSpentInMinutes;
      overTimeSpentInMinutes = result.overTimeSpentInMinutes;
      calculatedEndTime = result.endTime;
    }

    return this.attendance.findByIdAndUpdate(
      id,
      {
        user: userId,
        date,
        startTime: shift.startDateTime, // Always use shift start time for calculations
        endTime: calculatedEndTime,
        overTime,
        actualClockInTime: startTime, // Store provided startTime as actual clock-in
        actualClockOutTime: endTime, // Store provided endTime as actual clock-out
        timeSpentInMinutes,
        overTimeSpentInMinutes,
        location: locationId,
        shift: shiftId,
      },
      {
        new: true,
      },
    );
  }

  getAllAttendances(filter?: AttendanceFilterInput) {
    const query: FilterQuery<Attendance> = {};

    if (filter) {
      // Location filter
      if (filter.locationIds && filter.locationIds.length > 0)
        query.location = { $in: filter.locationIds };

      // Date range filter
      if (filter.dateRange) {
        const dateFilter: any = {};
        if (filter.dateRange.startDate) {
          dateFilter.$gte = filter.dateRange.startDate;
        }
        if (filter.dateRange.endDate) {
          dateFilter.$lte = filter.dateRange.endDate;
        }
        if (Object.keys(dateFilter).length > 0) {
          query.date = dateFilter;
        }
      }

      // User filter
      if (filter.userIds && filter.userIds.length > 0) {
        query.user = { $in: filter.userIds };
      }

      // Time spent filter (less than or equal to)
      if (filter.timeSpentLte !== undefined) {
        query.timeSpentInMinutes = { $lte: filter.timeSpentLte };
      }
    }

    return this.attendance.find(query);
  }

  getUserAttendances(userId: string, filter?: AttendanceFilterInput) {
    const query: FilterQuery<Attendance> = {
      user: userId, // Always filter by the specific user ID
    };

    if (filter) {
      // Location filter
      if (filter.locationIds && filter.locationIds.length > 0)
        query.location = { $in: filter.locationIds };

      // Date range filter
      if (filter.dateRange) {
        const dateFilter: any = {};
        if (filter.dateRange.startDate) {
          dateFilter.$gte = filter.dateRange.startDate;
        }
        if (filter.dateRange.endDate) {
          dateFilter.$lte = filter.dateRange.endDate;
        }
        if (Object.keys(dateFilter).length > 0) {
          query.date = dateFilter;
        }
      }

      // Note: userIds filter is ignored since we're already filtering by the specific user
      // Time spent filter (less than or equal to)
      if (filter.timeSpentLte !== undefined) {
        query.timeSpentInMinutes = { $lte: filter.timeSpentLte };
      }
    }

    return this.attendance.find(query);
  }
}

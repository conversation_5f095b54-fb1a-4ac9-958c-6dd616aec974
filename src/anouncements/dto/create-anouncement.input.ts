import { InputType, Field } from '@nestjs/graphql';
import { UserRoles } from 'src/users/entities/user.entity';

@InputType()
export class CreateAnouncementInput {
  @Field(() => [String], { nullable: true })
  users?: string[]; // Expecting an array of User ObjectIds as strings

  @Field(() => [UserRoles], { nullable: true })
  userRoles?: UserRoles[];

  @Field(() => String)
  title: string;

  @Field(() => String)
  description: string;

  @Field(() => Date)
  date: Date;

  @Field(() => String, { nullable: true })
  document?: string;

  @Field(() => String, { nullable: true })
  createdBy?: string; // Optional - will be auto-set from current user if not provided
}
